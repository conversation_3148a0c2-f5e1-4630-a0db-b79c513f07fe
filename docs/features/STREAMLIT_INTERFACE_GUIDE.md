# Streamlit Web 界面使用指南

## 🎨 界面概述

GuiXiaoXiRag FastAPI 提供了基于 Streamlit 的 Web 管理界面，让您可以通过直观的图形界面管理知识库、执行查询和监控系统状态。

## 🚀 启动 Web 界面

### 方式一：使用启动脚本（推荐）
```bash
# 启动 Streamlit 界面
python start_streamlit.py

# 指定端口启动
python start_streamlit.py --server.port 8501
```

### 方式二：直接使用 Streamlit
```bash
# 使用 streamlit 命令
streamlit run start_streamlit.py

# 指定配置启动
streamlit run start_streamlit.py --server.port 8501 --server.address 0.0.0.0
```

### 访问界面
启动成功后，在浏览器中访问：
- **本地访问**: http://localhost:8501
- **网络访问**: http://your-server-ip:8501

## 📋 界面功能

### 🏠 欢迎页面
- **系统概览**: 显示服务状态和基本信息
- **快速导航**: 提供各功能模块的快速入口
- **使用统计**: 展示系统使用情况和性能指标
- **公告信息**: 显示重要通知和更新信息

### 📊 系统状态
- **服务健康**: 实时显示 API 服务健康状态
- **性能指标**: CPU、内存、磁盘使用情况
- **连接状态**: 大模型服务连接状态
- **系统信息**: 版本信息、配置摘要

#### 功能特性
- 🔄 **自动刷新**: 每30秒自动更新状态信息
- 📈 **图表展示**: 直观的图表显示性能趋势
- ⚠️ **状态告警**: 异常状态自动高亮显示
- 📋 **详细信息**: 点击查看详细的系统信息

### 📚 文档管理
- **文本插入**: 直接输入文本内容到知识库
- **文件上传**: 支持多种格式文件上传
- **批量处理**: 一次性处理多个文档
- **处理状态**: 实时显示文档处理进度

#### 支持的文件格式
- **文本文件**: .txt, .md, .rst
- **文档文件**: .pdf, .docx, .doc
- **数据文件**: .json, .xml, .csv
- **代码文件**: .py, .js, .java, .cpp

#### 使用步骤
1. **选择输入方式**: 文本输入或文件上传
2. **设置参数**: 文档ID、语言、知识库等
3. **执行处理**: 点击提交开始处理
4. **查看结果**: 实时查看处理状态和结果

### 🔍 智能查询
- **多模式查询**: 支持6种不同的查询模式
- **参数调节**: 灵活调整查询参数
- **结果展示**: 清晰展示查询结果和相关信息
- **历史记录**: 保存查询历史便于回顾

#### 查询模式说明
- **hybrid**: 混合模式，平衡速度和质量（推荐）
- **local**: 本地模式，专注上下文相关信息
- **global**: 全局模式，利用全局知识
- **naive**: 朴素模式，基本搜索功能
- **mix**: 混合模式，整合知识图谱和向量检索
- **bypass**: 绕过模式，直接返回结果

#### 高级参数
- **top_k**: 返回结果数量（1-50）
- **max_tokens**: 最大token限制
- **enable_rerank**: 启用结果重排序
- **stream**: 流式返回结果

### 🗄️ 知识库管理
- **知识库列表**: 查看所有可用知识库
- **创建知识库**: 创建新的知识库
- **切换知识库**: 动态切换当前使用的知识库
- **知识库统计**: 查看知识库详细统计信息

#### 管理功能
- **📊 统计信息**: 文档数量、节点数、边数、存储大小
- **🔄 切换操作**: 一键切换到不同知识库
- **➕ 创建向导**: 引导式创建新知识库
- **🗑️ 清理功能**: 清理和重置知识库数据

### 🌍 语言设置
- **语言选择**: 选择系统回答语言
- **支持语言**: 中文、英文等多种语言
- **实时切换**: 立即生效的语言切换
- **语言测试**: 测试不同语言的查询效果

#### 支持的语言
- **中文**: 中文、Chinese、zh、zh-CN
- **英文**: 英文、English、en、en-US

### ⚙️ 服务配置
- **配置查看**: 查看当前服务配置
- **参数调整**: 调整部分运行时参数
- **配置验证**: 验证配置的有效性
- **配置导出**: 导出当前配置信息

#### 配置项目
- **服务信息**: 主机、端口、工作目录
- **模型配置**: LLM和Embedding模型设置
- **性能参数**: 缓存、并发等性能设置
- **安全设置**: CORS、文件大小限制等

### 📊 监控面板
- **实时监控**: 系统性能实时监控
- **图表展示**: 多种图表展示监控数据
- **告警信息**: 异常情况告警提示
- **历史数据**: 查看历史监控数据

#### 监控指标
- **请求统计**: 总请求数、成功率、响应时间
- **资源使用**: CPU、内存、磁盘使用率
- **服务状态**: API服务、大模型服务状态
- **错误统计**: 错误类型、频率统计

## 🎨 界面特性

### 🎯 用户体验
- **响应式设计**: 适配不同屏幕尺寸
- **直观操作**: 简单易用的操作界面
- **实时反馈**: 操作结果实时显示
- **错误提示**: 清晰的错误信息和解决建议

### ⚡ 性能优化
- **缓存机制**: 智能缓存提升响应速度
- **异步加载**: 异步加载提升用户体验
- **分页显示**: 大量数据分页显示
- **懒加载**: 按需加载减少资源消耗

### 🔒 安全特性
- **输入验证**: 严格的输入参数验证
- **错误处理**: 完善的错误处理机制
- **会话管理**: 安全的会话状态管理
- **访问控制**: 基础的访问权限控制

## 🛠️ 自定义配置

### 主题配置
在 `.env` 文件中配置界面主题：
```env
# Streamlit主题配置
STREAMLIT_PRIMARY_COLOR=#FF6B6B
STREAMLIT_BACKGROUND_COLOR=#FFFFFF
STREAMLIT_SECONDARY_BACKGROUND_COLOR=#F0F2F6
STREAMLIT_TEXT_COLOR=#262730
```

### 界面配置
```env
# 界面行为配置
STREAMLIT_MAX_UPLOAD_SIZE=50
STREAMLIT_ITEMS_PER_PAGE=10
STREAMLIT_AUTO_REFRESH_INTERVAL=30
```

### API配置
```env
# API连接配置
STREAMLIT_API_URL=http://localhost:8002
STREAMLIT_API_TIMEOUT=120
STREAMLIT_API_RETRY_TIMES=3
```

## 🔧 故障排除

### 常见问题

#### 界面无法访问
```bash
# 检查服务状态
ps aux | grep streamlit

# 检查端口占用
lsof -i :8501

# 重启服务
python start_streamlit.py
```

#### API连接失败
```bash
# 检查API服务状态
curl http://localhost:8002/health

# 检查配置
python scripts/config_manager.py --validate
```

#### 文件上传失败
- 检查文件大小是否超过限制
- 确认文件格式是否支持
- 查看错误提示信息

### 性能优化

#### 提升响应速度
- 启用缓存功能
- 减少同时显示的数据量
- 使用更快的查询模式

#### 减少资源消耗
- 关闭不需要的自动刷新
- 减少图表更新频率
- 优化查询参数

## 🔗 相关文档

- [快速开始指南](../getting-started/QUICK_START.md)
- [配置指南](../getting-started/CONFIGURATION_GUIDE.md)
- [API文档](../api/README.md)
- [主启动器指南](MAIN_LAUNCHER_GUIDE.md)

## 💡 使用技巧

1. **批量操作**: 使用批量功能提高处理效率
2. **参数调优**: 根据需求调整查询参数
3. **监控关注**: 定期查看系统监控信息
4. **配置备份**: 定期备份重要配置信息
5. **日志查看**: 遇到问题时查看详细日志
