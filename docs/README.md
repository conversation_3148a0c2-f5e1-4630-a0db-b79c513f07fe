# GuiXiaoXiRag FastAPI 文档中心

欢迎来到 GuiXiaoXiRag FastAPI 的文档中心！这里包含了项目的完整文档，采用分类组织的方式，帮助您快速找到所需信息。

## 📁 文档结构

```
docs/
├── 📁 getting-started/     # 🚀 快速上手
│   ├── QUICK_START.md      # 5分钟快速开始
│   ├── CONFIGURATION_GUIDE.md # 详细配置指南
│   ├── DEPLOYMENT_GUIDE.md # 生产环境部署
│   └── TROUBLESHOOTING.md  # 故障排除指南
│
├── 📁 api/                 # 📚 API文档
│   ├── API_REFERENCE.md    # 完整API参考
│   ├── API_EXAMPLES.md     # 实用调用示例
│   └── README.md           # API文档导航
│
├── 📁 features/            # 🌟 功能指南
│   ├── STREAMLIT_INTERFACE_GUIDE.md # Web界面指南
│   ├── MAIN_LAUNCHER_GUIDE.md # 主启动器指南
│   ├── KNOWLEDGE_BASE_LANGUAGE_FEATURES.md # 多语言功能
│   └── README.md           # 功能指南导航
│
├── 📁 project/             # 📊 项目信息
│   ├── PROJECT_ARCHITECTURE.md # 项目架构详解
│   ├── PROJECT_SUMMARY.md  # 项目概览总结
│   ├── CONFIG_OPTIMIZATION_SUMMARY.md # 配置优化记录
│   ├── DOCUMENTATION_OPTIMIZATION_SUMMARY.md # 文档优化记录
│   └── README.md           # 项目信息导航
│
└── README.md               # 📖 文档中心首页
```

## 🎯 快速导航

### 🚀 我想快速开始使用
- **[5分钟快速开始](getting-started/QUICK_START.md)** - 最快上手方式
- **[配置指南](getting-started/CONFIGURATION_GUIDE.md)** - 详细配置说明
- **[Web界面指南](features/STREAMLIT_INTERFACE_GUIDE.md)** - 图形界面使用

### 📚 我要集成API
- **[API文档中心](api/README.md)** - API文档导航
- **[完整API参考](api/API_REFERENCE.md)** - 所有接口详情
- **[API调用示例](api/API_EXAMPLES.md)** - 实用代码示例

### 🚀 我要部署到生产环境
- **[部署指南](getting-started/DEPLOYMENT_GUIDE.md)** - 生产环境部署
- **[配置指南](getting-started/CONFIGURATION_GUIDE.md)** - 配置优化
- **[故障排除](getting-started/TROUBLESHOOTING.md)** - 问题解决

### 🔧 我遇到了问题
- **[故障排除指南](getting-started/TROUBLESHOOTING.md)** - 常见问题解决
- **[配置指南](getting-started/CONFIGURATION_GUIDE.md)** - 配置问题排查
- **在线API文档**: [http://localhost:8002/docs](http://localhost:8002/docs)

### 🌟 我想了解功能特性
- **[功能指南中心](features/README.md)** - 功能导航
- **[Web界面指南](features/STREAMLIT_INTERFACE_GUIDE.md)** - 界面功能详解
- **[多语言功能](features/KNOWLEDGE_BASE_LANGUAGE_FEATURES.md)** - 语言和知识库

### 📊 我想了解项目架构
- **[项目信息中心](project/README.md)** - 项目信息导航
- **[项目架构详解](project/PROJECT_ARCHITECTURE.md)** - 技术架构
- **[项目概览总结](project/PROJECT_SUMMARY.md)** - 特性总结

## 📋 推荐阅读路径

### 👤 新用户路径
```
快速开始 → Web界面 → 故障排除
    ↓         ↓         ↓
QUICK_START → STREAMLIT → TROUBLESHOOTING
```

### 👨‍💻 开发者路径
```
API文档 → 调用示例 → 项目架构
   ↓        ↓         ↓
API_REF → EXAMPLES → ARCHITECTURE
```

### 🚀 运维人员路径
```
配置指南 → 部署指南 → 故障排除
   ↓        ↓         ↓
CONFIG → DEPLOYMENT → TROUBLESHOOTING
```

### 🏗️ 架构师路径
```
项目架构 → 功能特性 → 优化记录
   ↓        ↓         ↓
ARCH → FEATURES → OPTIMIZATION
```

## 🔗 在线资源

### 📖 交互式文档
- **Swagger UI**: [http://localhost:8002/docs](http://localhost:8002/docs) - 可测试的API文档
- **ReDoc**: [http://localhost:8002/redoc](http://localhost:8002/redoc) - 美观的API文档

### 🎨 管理界面
- **Streamlit界面**: [http://localhost:8501](http://localhost:8501) - Web管理界面

### 🔧 系统接口
- **健康检查**: [http://localhost:8002/health](http://localhost:8002/health)
- **系统状态**: [http://localhost:8002/system/status](http://localhost:8002/system/status)

## 📞 获取帮助

### 🔍 自助解决
1. **查看对应分类文档** - 根据问题类型选择相应目录
2. **使用故障排除指南** - [getting-started/TROUBLESHOOTING.md](getting-started/TROUBLESHOOTING.md)
3. **检查配置设置** - [getting-started/CONFIGURATION_GUIDE.md](getting-started/CONFIGURATION_GUIDE.md)
4. **测试API接口** - 访问 [http://localhost:8002/docs](http://localhost:8002/docs)

### 🤝 社区支持
- 提交Issue到项目仓库
- 查看项目Wiki和讨论区
- 参考示例代码和最佳实践

## 📝 文档贡献

我们欢迎您为文档做出贡献：

### 🔧 改进建议
- 发现错误或过时信息
- 提供更好的示例
- 改进文档结构
- 添加使用场景

### 📋 贡献方式
1. Fork项目仓库
2. 创建文档分支
3. 提交改进内容
4. 发起Pull Request

## 🎉 开始使用

选择适合您的入口开始探索：

- **🚀 快速体验**: [getting-started/QUICK_START.md](getting-started/QUICK_START.md)
- **📚 API集成**: [api/README.md](api/README.md)
- **🎨 界面操作**: [features/STREAMLIT_INTERFACE_GUIDE.md](features/STREAMLIT_INTERFACE_GUIDE.md)
- **🚀 生产部署**: [getting-started/DEPLOYMENT_GUIDE.md](getting-started/DEPLOYMENT_GUIDE.md)

感谢您选择 GuiXiaoXiRag FastAPI！🎊
